/* ===== NAVIGATION STYLES ===== */

/* Navbar styling */
.site-navbar {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.site-navbar .nav-link, 
.site-navbar .site-logo a {
    color: #ffffff !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.site-navbar .nav-link:hover, 
.site-navbar .site-logo a:hover {
    color: #4CAF50 !important;
    transform: translateY(-1px);
}

/* Dropdown hover effect */
.site-menu .dropdown:hover > .dropdown-menu {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transition: all 0.2s;
}

.site-menu .dropdown-menu {
    display: none;
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    margin-top: 0.5rem;
    border: none;
}

/* Modern cities dropdown design - Style similaire à l'image */
.villes-dropdown {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr);
    gap: 0;
    width: 800px;
    min-width: 800px;
    max-width: 90vw;
    padding: 40px;
    box-sizing: border-box;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 0;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    border: none;
}

.villes-dropdown li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.villes-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 18px 25px;
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
    font-weight: 400;
    font-size: 16px;
    color: #ecf0f1;
    background: transparent;
    text-decoration: none;
    position: relative;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.villes-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

.villes-dropdown .dropdown-item::before {
    content: "📍";
    color: #e67e22;
    font-size: 14px;
    transition: all 0.3s ease;
}

.villes-dropdown .dropdown-item:hover {
    background: rgba(230, 126, 34, 0.1);
    color: #e67e22;
    padding-left: 35px;
}

.villes-dropdown .dropdown-item:hover::before {
    color: #d35400;
    transform: scale(1.2);
}

/* Responsive design */
@media screen and (max-width: 1200px) {
    .villes-dropdown {
        grid-template-columns: repeat(2, 1fr);
        width: 600px;
        min-width: 600px;
        padding: 30px;
    }
}

@media screen and (max-width: 992px) {
    .villes-dropdown {
        grid-template-columns: repeat(1, 1fr);
        width: 400px;
        min-width: 400px;
        padding: 25px;
    }
}

@media screen and (max-width: 768px) {
    .villes-dropdown {
        grid-template-columns: repeat(1, 1fr);
        width: 300px;
        min-width: 300px;
        padding: 20px;
    }

    .villes-dropdown .dropdown-item {
        font-size: 14px;
        padding: 15px 20px;
    }
}

/* Additional navbar improvements */
.site-navbar .dropdown-menu {
    border: none;
}

/* Fix for Bootstrap conflicts */
.dropdown-menu.show {
    display: grid !important;
}

/* Ensure proper z-index for dropdown */
.site-navigation {
    position: relative;
    z-index: 999;
}
