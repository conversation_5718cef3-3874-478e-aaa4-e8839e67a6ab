<header class="site-navbar py-4 js-sticky-header site-navbar-target" role="banner">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-6 col-xl-2">
                <h1 class="mb-0 site-logo m-0 p-0"><a href="index.html" class="mb-0">LegalSpot</a></h1>
            </div>
            <div class="col-12 col-md-10 d-none d-xl-block">
                <nav class="site-navigation position-relative text-right" role="navigation">
                    <ul class="site-menu main-menu js-clone-nav mr-auto d-none d-lg-block">
                        <li><a href="#home-section" class="nav-link">Accueil</a></li>
                        <li><a href="#lots-section" class="nav-link">Lots</a></li>
                        <li><a href="#howitworks-section" class="nav-link">Comment ça marche</a></li>
                        <li><a href="#about-section" class="nav-link">À propos</a></li>
                        <li><a href="#news-section" class="nav-link">Actualités</a></li>
                        <li class="nav-item dropdown position-relative">
                            <a href="#" class="nav-link dropdown-toggle" id="villeDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="cursor:pointer;">
                                📍 Ville
                            </a>
                            <ul class="dropdown-menu villes-dropdown" aria-labelledby="villeDropdown">
                                <li><a class="dropdown-item ville-item" href="#ville-ariana">📍 Ariana</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-beja">📍 Béja</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-benarous">📍 Ben Arous</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-bizerte">📍 Bizerte</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-gabes">📍 Gabès</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-gafsa">📍 Gafsa</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-jendouba">📍 Jendouba</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-kairouan">📍 Kairouan</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-kasserine">📍 Kasserine</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-kebili">📍 Kébili</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-lekef">📍 Le Kef</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-mahdia">📍 Mahdia</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-manouba">📍 La Manouba</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-medenine">📍 Médenine</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-monastir">📍 Monastir</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-nabeul">📍 Nabeul</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-sfax">📍 Sfax</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-sidibouzid">📍 Sidi Bouzid</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-siliana">📍 Siliana</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-sousse">📍 Sousse</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-tataouine">📍 Tataouine</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-tozeur">📍 Tozeur</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-tunis">📍 Tunis</a></li>
                                <li><a class="dropdown-item ville-item" href="#ville-zaghouan">📍 Zaghouan</a></li>
                            </ul>
                        </li>
                        <li><a href="#contact-section" class="nav-link">Contact</a></li>
                    </ul>
                </nav>
            </div>
            <div class="col-6 d-inline-block d-xl-none ml-md-0 py-3"><a href="#" class="site-menu-toggle js-menu-toggle text-black float-right"><span class="icon-menu h3"></span></a></div>
        </div>
    </div>
</header>
<style>
/* Navbar styling */
.site-navbar {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.site-navbar .nav-link, .site-navbar .site-logo a {
    color: #ffffff !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.site-navbar .nav-link:hover, .site-navbar .site-logo a:hover {
    color: #4CAF50 !important;
    transform: translateY(-1px);
}

/* Dropdown hover effect */
.site-menu .dropdown:hover > .dropdown-menu {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.site-menu .dropdown-menu {
    display: none;
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%) translateY(10px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    margin-top: 0.5rem;
    border: none;
    transition: all 0.3s ease;
}

/* Modern cities dropdown design */
.villes-dropdown {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px;
    width: 720px;
    min-width: 720px;
    max-width: 90vw;
    padding: 32px;
    box-sizing: border-box;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.villes-dropdown li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.villes-dropdown .dropdown-item {
    display: block;
    padding: 14px 18px;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
    color: #2c3e50;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(76, 175, 80, 0.1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.villes-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: #ffffff;
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 25px rgba(76, 175, 80, 0.3),
        0 4px 12px rgba(76, 175, 80, 0.2);
    border-color: #4CAF50;
}

/* Responsive design */
@media screen and (max-width: 1200px) {
    .villes-dropdown {
        grid-template-columns: repeat(3, 1fr);
        width: 600px;
        min-width: 600px;
        gap: 10px;
        padding: 28px;
    }

    .villes-dropdown .dropdown-item {
        padding: 12px 16px;
        font-size: 13px;
    }
}

@media screen and (max-width: 992px) {
    .villes-dropdown {
        grid-template-columns: repeat(2, 1fr);
        width: 420px;
        min-width: 420px;
        gap: 8px;
        padding: 24px;
    }

    .villes-dropdown .dropdown-item {
        padding: 10px 14px;
        font-size: 12px;
    }
}

@media screen and (max-width: 768px) {
    .villes-dropdown {
        grid-template-columns: repeat(1, 1fr);
        width: 280px;
        min-width: 280px;
        gap: 6px;
        padding: 20px;
    }
}

/* Additional navbar improvements */
.site-navbar .dropdown-toggle::after {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.site-navbar .dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
}

.site-navbar .dropdown-menu {
    border: none;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
</style>
